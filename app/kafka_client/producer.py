import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional  # Import Optional

from aiokafka import AIOKafkaProducer  # type: ignore

from ..schemas.kafka import TokenUsageEvent
from ..shared.config.base import get_settings


# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class KafkaProducer:

    def __init__(self) -> None:
        self.logger = logger
        self.settings = get_settings()
        self.kafka_broker: str = self.settings.kafka.kafka_bootstrap_servers
        # Initialize producer to None. It will be created in start_producer
        self.producer: Optional[AIOKafkaProducer] = None
        self.logger.info(
            "Kafka producer class initialized (producer object not created yet)"
        )

    async def start_producer(self) -> None:
        # Check if producer already exists (optional, but good practice)
        if self.producer is not None:
            self.logger.warning("Producer already started.")
            return

        try:
            # Create the AIOKafkaProducer instance here, inside the async method
            self.producer = AIOKafkaProducer(
                bootstrap_servers=self.kafka_broker,
                max_request_size=524288000,  # Consider if this large size is truly needed
                value_serializer=lambda v: json.dumps(v).encode("utf-8"),
                # loop=asyncio.get_running_loop() # Usually not needed anymore, aiokafka finds it
            )
            # Start the producer
            await self.producer.start()
            self.logger.info("Kafka producer created and started successfully")
        except Exception as e:
            self.logger.error(f"Failed to create or start Kafka producer: {e}")
            # Optional: re-raise the exception if startup failure should stop the app
            # raise

    async def stop_producer(self) -> None:
        if self.producer:
            await self.producer.stop()
            self.producer = None  # Clear the reference
            self.logger.info("Kafka producer stopped")
        else:
            self.logger.warning(
                "Attempted to stop a producer that was not running or initialized."
            )

    async def send_message(
        self,
        topic: str,
        message: Dict[str, Any],
        headers: Optional[List[tuple[str, bytes]]] = None,
    ) -> None:

        self.logger.debug(f"Attempting to send message to topic '{topic}'")

        # Ensure the producer is started before sending
        if not self.producer:
            self.logger.error(
                f"Cannot send message to topic '{topic}': Producer not started."
            )
            # Depending on requirements, you might raise an exception here
            # raise RuntimeError("Producer not started")
            return

        try:
            await self.producer.send(
                topic,
                message,
                headers=headers,
            )
            # Consider logging message content carefully (potential PII/size issues)
            self.logger.debug(f"Message sent to topic '{topic}'")
            # Or just: self.logger.info(f"Message sent to topic '{topic}'")
        except Exception as e:
            self.logger.error(f"Error sending message to topic '{topic}': {str(e)}")

    async def send_token_usage(
        self,
        event_id: str,
        organisation_id: Optional[str],
        user_id: str,
        input_tokens: int,
        output_tokens: int,
        model_id: Optional[str] = None,
        agent_id: Optional[str] = None,
    ) -> None:
        """
        Send token usage data to the token usage topic using TokenUsageEvent schema.

        Args:
            event_id: Unique event identifier (UUID4 string or any string that will be converted to UUID)
            organisation_id: Organisation identifier
            user_id: User identifier
            input_tokens: Number of input/prompt tokens used
            output_tokens: Number of output/completion tokens used
            model_id: Optional model identifier
            agent_id: Optional agent identifier
        """

        # Create TokenUsageEvent
        token_usage_event = TokenUsageEvent(
            event_id=event_id,
            organisation_id=organisation_id,
            user_id=user_id,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            model_id=model_id,
            agent_id=agent_id,
            timestamp=datetime.utcnow(),
        )

        # Convert to dict for sending
        token_usage_message = token_usage_event.model_dump()

        # Convert UUID and datetime to strings for JSON serialization
        token_usage_message["event_id"] = str(token_usage_message["event_id"])
        token_usage_message["timestamp"] = (
            token_usage_message["timestamp"].isoformat() + "Z"
        )

        # Send the token usage message
        await self.send_message(
            topic=self.settings.kafka.kafka_token_usage_topic,
            message=token_usage_message,
        )

        self.logger.info(
            f"📈 Token usage published to Kafka - Event ID: {event_id}, "
            f"Input tokens: {input_tokens}, "
            f"Output tokens: {output_tokens}, "
            f"Total tokens: {input_tokens + output_tokens}, "
            f"User ID: {user_id}, "
            f"Model ID: {model_id}"
        )

    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format"""

        return datetime.utcnow().isoformat() + "Z"

    def decode_headers(self, headers_list: List[tuple[bytes, bytes]]) -> Dict[str, str]:
        # This method doesn't interact with the producer, so it's fine
        decoded_headers: Dict[str, str] = {}
        for key_bytes, value_bytes in headers_list:
            # Ensure robust decoding, handle potential errors
            key = (
                key_bytes.decode("utf-8", errors="replace")
                if isinstance(key_bytes, bytes)
                else str(key_bytes)
            )
            value = (
                value_bytes.decode("utf-8", errors="replace")
                if isinstance(value_bytes, bytes)
                else str(value_bytes)
            )
            decoded_headers[key] = value
        return decoded_headers

    # This function remains the same, it calls the async start_producer
    async def init_kafka_producer(self) -> None:
        await self.start_producer()


# Instantiate the wrapper class (this is fine now as __init__ is lightweight)
kafka_producer = KafkaProducer()
