#!/usr/bin/env python3
"""
Example demonstrating the comprehensive model factory capabilities.

This example shows how to use the updated ModelFactory to create clients
for all supported AutoGen model providers including:
- OpenAI (direct and via Requesty routing)
- Anthropic
- Azure OpenAI
- Azure AI (GitHub models, Azure AI Foundry)
- Ollama (local models)

The factory supports Requesty routing for unified access to 300+ models
from multiple providers through a single API.
"""

import asyncio
import logging
import os
import sys
from typing import Any, Dict

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), "../.."))

from autogen_core.models import UserMessage

from app.autogen_service.model_factory import ModelFactory

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_openai_direct():
    """Test direct OpenAI model client."""
    logger.info("Testing direct OpenAI model client...")

    config = {
        "provider": "OpenAIChatCompletionClient",
        "llm_type": "openai",
        "model": "gpt-4o-mini",
        "api_key": os.getenv("OPENAI_API_KEY"),
        "temperature": 0.7,
        "max_tokens": 100,
    }

    client = ModelFactory.create_model_client(config)
    if client:
        try:
            response = await client.create(
                [UserMessage(content="Hello! How are you?", source="user")]
            )
            logger.info(f"OpenAI Response: {response.content}")
        except Exception as e:
            logger.error(f"Error with OpenAI client: {e}")
        finally:
            await client.close()
    else:
        logger.error("Failed to create OpenAI client")


async def test_requesty_routing():
    """Test Requesty routing with multiple providers."""
    logger.info("Testing Requesty routing...")

    # First, fetch available models from Requesty
    try:
        available_models = await ModelFactory.fetch_requesty_models()
        logger.info(f"Found {len(available_models)} models available through Requesty")

        # Show some example models by provider
        models_by_provider = {}
        for model_data in available_models[:10]:  # Show first 10 models
            provider = model_data.get("provider", "unknown")
            model_name = model_data.get("model", "")
            if provider not in models_by_provider:
                models_by_provider[provider] = []
            models_by_provider[provider].append(model_name)

        logger.info("Sample models by provider:")
        for provider, models in models_by_provider.items():
            logger.info(
                f"  {provider}: {models[:3]}"
            )  # Show first 3 models per provider

    except Exception as e:
        logger.error(f"Error fetching Requesty models: {e}")
        available_models = []

    # Test different models through Requesty
    models_to_test = [
        "anthropic/claude-3-haiku-********",
        "google/gemini-1.5-flash",
        "meta/llama-3.2-3b-instruct",
        "mistral/mistral-7b-instruct",
    ]

    for model in models_to_test:
        logger.info(f"Testing model: {model}")

        # Check if model is supported
        if not ModelFactory.is_model_supported(model):
            logger.warning(f"Model {model} not found in Requesty, skipping")
            continue

        config = {
            "provider": "OpenAIChatCompletionClient",
            "llm_type": "openai",  # Use OpenAI client for Requesty routing
            "model": model,
            # API key and base_url will default to Requesty settings
            "temperature": 0.5,
            "max_tokens": 50,
        }

        client = ModelFactory.create_model_client(config)
        if client:
            try:
                response = await client.create(
                    [UserMessage(content="What is AI?", source="user")]
                )
                logger.info(f"{model} Response: {response.content[:100]}...")
            except Exception as e:
                logger.error(f"Error with {model}: {e}")
            finally:
                await client.close()
        else:
            logger.error(f"Failed to create client for {model}")


async def test_anthropic_direct():
    """Test direct Anthropic model client."""
    logger.info("Testing direct Anthropic model client...")

    config = {
        "provider": "AnthropicChatCompletionClient",
        "model": "claude-3-haiku-********",
        "api_key": os.getenv("ANTHROPIC_API_KEY"),
        "temperature": 0.3,
        "max_tokens": 100,
    }

    client = ModelFactory.create_model_client(config)
    if client:
        try:
            response = await client.create(
                [
                    UserMessage(
                        content="Explain quantum computing briefly.", source="user"
                    )
                ]
            )
            logger.info(f"Anthropic Response: {response.content}")
        except Exception as e:
            logger.error(f"Error with Anthropic client: {e}")
        finally:
            await client.close()
    else:
        logger.error("Failed to create Anthropic client")


async def test_azure_openai():
    """Test Azure OpenAI model client."""
    logger.info("Testing Azure OpenAI model client...")

    config = {
        "provider": "AzureOpenAIChatCompletionClient",
        "model": "gpt-4",
        "azure_deployment": os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME"),
        "azure_endpoint": os.getenv("AZURE_OPENAI_ENDPOINT"),
        "api_key": os.getenv("AZURE_OPENAI_API_KEY"),
        "api_version": "2024-06-01",
        "temperature": 0.5,
        "max_tokens": 100,
    }

    # Only test if Azure credentials are available
    if config["azure_deployment"] and config["azure_endpoint"] and config["api_key"]:
        client = ModelFactory.create_model_client(config)
        if client:
            try:
                response = await client.create(
                    [UserMessage(content="What is machine learning?", source="user")]
                )
                logger.info(f"Azure OpenAI Response: {response.content}")
            except Exception as e:
                logger.error(f"Error with Azure OpenAI client: {e}")
            finally:
                await client.close()
        else:
            logger.error("Failed to create Azure OpenAI client")
    else:
        logger.info("Skipping Azure OpenAI test - credentials not available")


async def test_azure_ai():
    """Test Azure AI model client (GitHub models)."""
    logger.info("Testing Azure AI model client...")

    config = {
        "provider": "AzureAIChatCompletionClient",
        "model": "Phi-4",
        "endpoint": "https://models.inference.ai.azure.com",
        "api_key": os.getenv("GITHUB_TOKEN"),  # GitHub PAT for GitHub models
        "temperature": 0.4,
        "max_tokens": 100,
        "model_info": {
            "json_output": False,
            "function_calling": False,
            "vision": False,
            "family": "unknown",
            "structured_output": False,
        },
    }

    # Only test if GitHub token is available
    if config["api_key"]:
        client = ModelFactory.create_model_client(config)
        if client:
            try:
                response = await client.create(
                    [
                        UserMessage(
                            content="What is the capital of France?", source="user"
                        )
                    ]
                )
                logger.info(f"Azure AI Response: {response.content}")
            except Exception as e:
                logger.error(f"Error with Azure AI client: {e}")
            finally:
                await client.close()
        else:
            logger.error("Failed to create Azure AI client")
    else:
        logger.info("Skipping Azure AI test - GitHub token not available")


async def test_ollama():
    """Test Ollama local model client."""
    logger.info("Testing Ollama local model client...")

    config = {
        "provider": "OllamaChatCompletionClient",
        "model": "llama3.2",
        "base_url": "http://localhost:11434",
        "temperature": 0.6,
        "max_tokens": 100,
    }

    client = ModelFactory.create_model_client(config)
    if client:
        try:
            response = await client.create(
                [UserMessage(content="Hello from Ollama!", source="user")]
            )
            logger.info(f"Ollama Response: {response.content}")
        except Exception as e:
            logger.error(f"Error with Ollama client (is Ollama running?): {e}")
        finally:
            await client.close()
    else:
        logger.error("Failed to create Ollama client")


def show_supported_providers():
    """Display information about all supported providers."""
    logger.info("Supported Model Providers:")
    logger.info("=" * 50)

    providers = ModelFactory.get_supported_providers()
    for provider_name, info in providers.items():
        logger.info(f"\n{provider_name}:")
        logger.info(f"  Description: {info['description']}")
        logger.info(f"  Supports Requesty: {info['supports_requesty']}")
        logger.info(f"  Example Models: {', '.join(info['example_models'])}")
        logger.info(f"  Required Config: {', '.join(info['required_config'])}")
        logger.info(f"  Optional Config: {', '.join(info['optional_config'])}")


async def show_all_available_models():
    """Display all models available through Requesty."""
    logger.info("Fetching all available models from Requesty...")
    logger.info("=" * 50)

    try:
        models_by_provider = ModelFactory.get_all_available_models()

        total_models = sum(len(models) for models in models_by_provider.values())
        logger.info(f"Total models available: {total_models}")
        logger.info(f"Providers: {len(models_by_provider)}")

        for provider, models in models_by_provider.items():
            logger.info(f"\n{provider} ({len(models)} models):")
            # Show first 5 models for each provider
            for model in models[:5]:
                capabilities = ModelFactory.get_model_capabilities_from_requesty(model)
                vision = "✓" if capabilities.get("vision") else "✗"
                reasoning = "✓" if capabilities.get("supports_reasoning") else "✗"
                caching = "✓" if capabilities.get("supports_caching") else "✗"
                logger.info(
                    f"  {model} (Vision: {vision}, Reasoning: {reasoning}, Caching: {caching})"
                )

            if len(models) > 5:
                logger.info(f"  ... and {len(models) - 5} more models")

    except Exception as e:
        logger.error(f"Error fetching models: {e}")


async def main():
    """Run all model factory examples."""
    logger.info("Starting Model Factory Examples")
    logger.info("=" * 50)

    # Show supported providers
    show_supported_providers()

    # Show all available models from Requesty
    await show_all_available_models()

    # Test different model clients
    await test_openai_direct()
    await test_requesty_routing()
    await test_anthropic_direct()
    await test_azure_openai()
    await test_azure_ai()
    await test_ollama()

    logger.info("Model Factory Examples Complete")


if __name__ == "__main__":
    asyncio.run(main())
