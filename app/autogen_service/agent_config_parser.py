import asyncio
import json
import logging
from typing import Any, Dict, List, Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_core import (
    AgentId,
    AgentRuntime,
    AgentType,
    RoutedAgent,
    SingleThreadedAgentRuntime,
    TypeSubscription,
)
from autogen_core.models import <PERSON>t<PERSON><PERSON>pletionClient, SystemMessage
from autogen_core.tools import <PERSON><PERSON><PERSON><PERSON>, Tool
from autogen_ext.models.openai import OpenAIChatCompletionClient

from app.autogen_service.ai_agent import AIAgent

from ..tools.tool_loader import ToolLoader
from .head_agent import HeadAgent
from .model_factory import ModelFactory

logger = logging.getLogger(__name__)


class AgentConfigParser:
    """
    Parses agent configuration JSON and registers agents dynamically.
    """

    def __init__(self, runtime: AgentRuntime, tool_loader: ToolLoader):
        """
        Initializes AgentConfigParser with a runtime and a ToolLoader instance.

        Args:
            runtime: The AgentRuntime instance to register agents with.
            tool_loader: The ToolLoader instance to load tools.
        """
        self.runtime = runtime
        self.tool_loader = tool_loader
        self.agent_category_to_class = {  # Mapping agent_category to agent classes
            "AIAgent": <PERSON><PERSON>gent,
            "Assistant": Assistant<PERSON>gent,
            "HeadAgent": HeadAgent,
        }

    async def parse_and_register_agent_from_config(
        self,
        agent_config_json: Dict[str, Any],
        registered_agent_types: Dict[str, AgentType] = None,
    ) -> Optional[AgentType]:
        """
        Parses an agent configuration from JSON and registers the agent in the runtime.

        Args:
            agent_config_json: A dictionary representing the agent configuration in JSON format.

        Returns:
            The registered AgentType object, or None if registration fails.
        """
        agent_id = agent_config_json.get("agent_id")
        agent_category = agent_config_json.get("agent_category")
        description = agent_config_json.get("description")
        system_message_content = agent_config_json.get("system_message")
        model_client_config = agent_config_json.get("model_client", {})
        agent_tools_configs = agent_config_json.get("agent_tools", [])
        agent_topic_type = agent_config_json.get("agent_topic_type")
        subscriptions_config = agent_config_json.get("subscriptions", [])
        delegate_tools = agent_config_json.get("delegate_tools", [])
        user_topic_type = agent_config_json.get("user_topic_type")

        # --- Validate Required Fields ---
        logger.info("Parsing agent configuration for agent_id: %s", agent_id)
        if not all(
            [
                agent_id,
                agent_category,
                description,
                system_message_content,
                model_client_config,
                agent_topic_type,
                subscriptions_config,
            ]
        ):

            return None

        agent_class = self.agent_category_to_class.get(agent_category)
        if not agent_class:

            return None

        # --- Initialize Model Client ---
        model_client = self._create_model_client(model_client_config)

        if not model_client:
            logger.error("Failed to initialize model client for agent_id: %s", agent_id)
            return None

        # --- Load Agent Tools ---
        agent_tools = await self._load_agent_tools(agent_tools_configs)

        # --- Create Agent Factory ---
        def agent_factory():
            agent_kwargs = {  # Use a dictionary to build keyword arguments
                "description": description,
                "system_message": SystemMessage(content=system_message_content),
                "model_client": model_client,
                "agent_topic_type": agent_topic_type,
                "delegate_tools": delegate_tools,
                "user_topic_type": user_topic_type,
                "registered_agent_types": registered_agent_types,
            }

            if agent_category in [
                "AIAgent",
                "Assistant",
            ]:  # Agent types that expect 'tools'
                agent_kwargs["tools"] = (
                    agent_tools  # Add 'tools' only for these agent types
                )

            return agent_class(**agent_kwargs)  # Pass keyword arguments using **

        try:
            # --- Register Agent Type ---
            registered_agent_type = await agent_class.register(
                self.runtime,
                type=agent_topic_type,
                factory=agent_factory,
            )

            # --- Add Subscriptions ---
            for subscription_def in subscriptions_config:
                topic_type = subscription_def.get("topic_type")
                if topic_type:
                    subscription = TypeSubscription(
                        topic_type=topic_type, agent_type=registered_agent_type.type
                    )
                    await self.runtime.add_subscription(subscription)
                else:
                    logger.warning(
                        "Subscription definition missing 'topic_type': %s",
                        subscription_def,
                    )

            logger.info("Agent '%s' registered successfully.", agent_id)
            return registered_agent_type

        except Exception as e:
            logger.exception("Error registering agent '%s':", agent_id)
            return None

    def _create_model_client(self, model_client_config):
        """
        Creates a model client based on the provided configuration.

        Args:
            model_client_config: Dictionary containing model configuration

        Returns:
            A ChatCompletionClient instance or None if there's an error.
        """
        # Extract model configuration from agent config
        provider = model_client_config.get("provider")
        model = model_client_config.get("model")
        api_key = model_client_config.get("api_key")

        # Create model configuration dictionary
        model_config = {"provider": provider, "model": model, "api_key": api_key}

        # Add optional parameters if present
        for param in ["temperature", "max_tokens", "base_url", "model_info"]:
            if param in model_client_config:
                model_config[param] = model_client_config[param]

        # Use ModelFactory to create the model client
        return ModelFactory.create_model_client(model_config)

    async def _load_agent_tools(
        self, agent_tools_configs: List[Dict[str, Any]]
    ) -> List[Tool]:
        """
        Loads and creates Tool instances for the agent from the tool configurations.

        Args:
            agent_tools_configs: List of tool configuration dictionaries.

        Returns:
            A list of Tool instances.
        """
        agent_tools: List[Tool] = []
        for tool_config in agent_tools_configs:
            logger.info("Loading tool from definition: %s", tool_config)
            tool_instance = await self.tool_loader.create_tool_from_definition(
                tool_config
            )
            if tool_instance:
                if asyncio.iscoroutine(tool_instance):
                    tool_instance = await tool_instance

                if not hasattr(tool_instance, "name"):
                    # If no name, use the tool's function name or a generated name
                    tool_instance.name = tool_config.get(
                        "name", f"tool_{len(agent_tools)}"
                    )

                agent_tools.append(tool_instance)
            # Non-critical error for tool loading
        return agent_tools

    async def _add_subscriptions(
        self, subscriptions_config: List[Dict[str, str]], agent_type: str
    ) -> None:
        """
        Adds type-based subscriptions to the runtime for the agent.

        Args:
            subscriptions_config: List of subscription configuration dictionaries.
            agent_type: The agent type to associate with the subscriptions.
        """
        for subscription_def in subscriptions_config:
            topic_type = subscription_def.get("topic_type")
            # agent_type from parameter is used, not from subscription_def (as per schema and requirement)
            if topic_type:
                subscription = TypeSubscription(
                    topic_type=topic_type, agent_type=agent_type
                )
                await self.runtime.add_subscription(subscription)
            else:
                logger.warning(
                    "Subscription definition missing 'topic_type': %s", subscription_def
                )
