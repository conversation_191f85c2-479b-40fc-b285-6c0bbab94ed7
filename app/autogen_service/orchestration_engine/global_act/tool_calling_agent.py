from __future__ import annotations

import logging
from typing import Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_core.models import ChatCompletionClient

from app.shared.config.base import Settings, get_settings

from ...model_factory import ModelFactory

logger = logging.getLogger(__name__)

# Disable autogen logging for cleaner output
for logger_name in [
    "autogen_agentchat",
    "autogen_core",
    "_single_threaded_agent_runtime",
    "autogen_runtime_core",
    "autogen_agentchat.teams",
    "autogen_agentchat.agents",
]:
    logger_to_silence = logging.getLogger(logger_name)
    logger_to_silence.setLevel(logging.CRITICAL)
    logger_to_silence.propagate = False


class ToolCallingEmployee:
    """
    Tool Calling Employee
    This agent is responsible for executing available tools to respond to user queries.
    It uses the tools at its disposal to provide comprehensive responses to user requests.
    """

    def __init__(self):
        """Initialize the ToolCallingEmployee with default configuration."""
        self._settings: Settings = get_settings()
        self._model_client: Optional[ChatCompletionClient] = None
        self._agent: Optional[AssistantAgent] = None
        self._is_initialized: bool = False

    async def initialize(self, tools: Optional[list[str]] = None) -> bool:
        """
        Initialize the Tool Calling Employee.
        """
        try:
            # Create model client using ModelFactory
            model_config = {
                "provider": "OpenAIChatCompletionClient",
                "llm_type": "openai",
                "model": "gpt-4.1",
                "api_key": self._settings.requesty.api_key,
                "base_url": self._settings.requesty.base_url,
                "model_info": {
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,
                },
            }

            self._model_client = ModelFactory.create_model_client(model_config)
            if not self._model_client:
                logger.error("Failed to create model client for Tool Calling Employee")
                return False

            self._agent = AssistantAgent(
                name="ToolCallingEmployee",
                description="A Tool Calling Employee that executes available tools to respond to user queries and provide comprehensive assistance.",
                model_client=self._model_client,
                tools=tools if tools else [],
                reflect_on_tool_use=True,
                system_message=self._get_enhanced_system_message(),
                max_tool_iterations=4,
            )

            self._is_initialized = True
            logger.info("Tool Calling Employee initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Tool Calling Employee : {str(e)}")
            self._is_initialized = False
            return False

    def _get_enhanced_system_message(self) -> str:
        """Get the enhanced system message for the tool calling agent."""

        return f"""You are a Tool Calling Employee that executes the tools available at your disposal to respond to user queries.
                    Use the provided tools to gather information, perform actions, and provide comprehensive assistance to help answer the user's questions effectively.
                    If the task cannot be completed with the available tools, inform politely that you are unable to assist with the request.
        """

    def get_agent(self) -> Optional[AssistantAgent]:
        """
        Get the underlying AssistantAgent instance.

        Returns:
            Optional[AssistantAgent]: The agent instance if initialized, None otherwise
        """
        return self._agent if self._is_initialized else None

    def is_initialized(self) -> bool:
        """
        Check if the agent is properly initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return self._is_initialized

    @classmethod
    async def create_and_initialize(
        cls, tools: Optional[list[str]] = None
    ) -> Optional["ToolCallingEmployee"]:
        """
        Convenience method to create and initialize the Tool Calling Employee in one call.

        Returns:
            ToolCallingEmployee: Initialized Tool Calling Employee
        """
        agent = cls()
        if await agent.initialize(tools):
            return agent
        else:
            logger.error("Failed to create and initialize ToolCallingEmployee")
            return None
