from enum import Enum


# Enum for SSE event types based on chat response structure
class SSEEventType(str, Enum):
    """Server-Sent Events types for real-time communication."""

    # Session management events
    SESSION_INITIALIZED = "session_initialized"

    # Message streaming events
    MESSAGE_STREAM_STARTED = "message_stream_started"
    MESSAGE_STREAMING = "message_streaming"
    MESSAGE_END = "message_end"

    MESSAGE_RESPONSE = "message_response"

    # MCP (Model Context Protocol) events
    MCP_EXECUTION_STARTED = "mcp_execution_started"
    MCP_EXECUTION_ENDED = "mcp_execution_ended"
    MCP_EXECUTION_FAILED = "mcp_execution_failed"

    # Workflow execution events
    WORKFLOW_EXECUTION_STARTED = "workflow_execution_started"
    WORKFLOW_EXECUTION_STEP = "workflow_execution_step"
    WORKFLOW_EXECUTION_COMPLETED = "workflow_execution_completed"
    WORKFLOW_EXECUTION_FAILED = "workflow_execution_failed"

    KN<PERSON><PERSON>DGE_FETCH_STARTED = "knowledge_fetch_started"
    KNOWLEDGE_FETCH_COMPLETED = "knowledge_fetch_completed"
    KNOWLEDGE_FETCH_FAILED = "knowledge_fetch_failed"

    # System events
    KEEP_ALIVE = "keep_alive"
    ERROR = "error"
    CONNECTION_CLOSED = "connection_closed"


class AgentType(Enum):
    GLOBAL_AGENT = "global_agent"
    PERSONA_AGENT = "persona_agent"
    USER_AGENT = "user_agent"
    AI_AGENT = "ai_agent"
    HEAD_AGENT = "head_agent"


class ErrorCode(str, Enum):
    """Error codes for standardized error handling across the application."""

    # General errors (1000-1099)
    UNKNOWN_ERROR = "ERR_1000"
    VALIDATION_ERROR = "ERR_1001"
    CONFIGURATION_ERROR = "ERR_1002"
    TIMEOUT_ERROR = "ERR_1003"
    NETWORK_ERROR = "ERR_1004"

    # Kafka errors (1100-1199)
    KAFKA_MESSAGE_PARSE_ERROR = "ERR_1100"
    KAFKA_PRODUCER_ERROR = "ERR_1101"
    KAFKA_CONSUMER_ERROR = "ERR_1102"
    KAFKA_TOPIC_ERROR = "ERR_1103"

    # Agent errors (1200-1299)
    AGENT_NOT_FOUND = "ERR_1200"
    AGENT_CONFIG_INVALID = "ERR_1201"
    AGENT_CREATION_FAILED = "ERR_1202"
    AGENT_INITIALIZATION_FAILED = "ERR_1203"
    AGENT_EXECUTION_FAILED = "ERR_1204"

    # Session errors (1300-1399)
    SESSION_NOT_FOUND = "ERR_1300"
    SESSION_CREATION_FAILED = "ERR_1301"
    SESSION_DELETION_FAILED = "ERR_1302"
    SESSION_EXPIRED = "ERR_1303"
    SESSION_INVALID_STATE = "ERR_1304"
    SESSION_STOP_ERROR = "ERR_1305"

    # Chat errors (1400-1499)
    CHAT_PROCESSING_FAILED = "ERR_1400"
    CHAT_MESSAGE_INVALID = "ERR_1401"
    CHAT_STREAM_ERROR = "ERR_1402"
    CHAT_CONTEXT_ERROR = "ERR_1403"

    # Group chat errors (1500-1599)
    GROUP_CHAT_NOT_FOUND = "ERR_1500"
    GROUP_CHAT_CREATION_FAILED = "ERR_1501"
    GROUP_CHAT_PROCESSING_FAILED = "ERR_1502"

    # Orchestration errors (1600-1699)
    ORCHESTRATION_SESSION_FAILED = "ERR_1600"
    ORCHESTRATION_CHAT_FAILED = "ERR_1601"
    ORCHESTRATION_TEAM_ERROR = "ERR_1602"

    # MCP errors (1700-1799)
    MCP_TOOL_ERROR = "ERR_1700"
    MCP_FETCH_FAILED = "ERR_1701"
    MCP_EXECUTION_ERROR = "ERR_1702"

    # Workflow errors (1800-1899)
    WORKFLOW_NOT_FOUND = "ERR_1800"
    WORKFLOW_EXECUTION_FAILED = "ERR_1801"
    WORKFLOW_FETCH_FAILED = "ERR_1802"

    # Human input errors (1900-1999)
    HUMAN_INPUT_TIMEOUT = "ERR_1900"
    HUMAN_INPUT_INVALID = "ERR_1901"
    HUMAN_INPUT_PROCESSING_FAILED = "ERR_1902"


class ErrorMessage:
    """Standardized error messages for different error scenarios."""

    # Agent creation errors
    AGENT_CONFIG_NOT_FOUND = "Agent configuration not found for the specified ID"
    AGENT_SESSION_CREATION_FAILED = "Failed to create agent session"
    GROUP_CHAT_SESSION_CREATION_FAILED = "Failed to create group chat session"
    AGENT_EXECUTION_FAILED = "Agent execution failed"

    # Agent initialization errors

    # Chat processing errors
    CHAT_SESSION_NOT_FOUND = "Chat session not found"
    CHAT_PROCESSING_ERROR = "Error occurred while processing chat request"
    CHAT_STREAM_ERROR = "Error occurred during chat streaming"

    # Session management errors
    SESSION_NOT_FOUND = "Session not found or has expired"
    SESSION_DELETION_ERROR = "Failed to delete session"
    SESSION_STOP_ERROR = "Failed to stop chat session"

    # Orchestration errors
    ORCHESTRATION_SESSION_ERROR = "Failed to create orchestration team session"
    ORCHESTRATION_CHAT_ERROR = "Failed to process orchestration team chat"

    # Human input errors
    HUMAN_INPUT_ERROR = "Failed to process human input response"

    # General errors
    MESSAGE_PARSE_ERROR = "Failed to parse message"
    VALIDATION_ERROR = "Request validation failed"
    UNKNOWN_TOPIC_ERROR = "Unknown Kafka topic"
