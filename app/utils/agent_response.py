from typing import List, Optional

from pydantic import BaseModel

from .enums import ContentType


class EmployeeProfile(BaseModel):
    id: str
    name: str
    description: str
    avatar: str
    agent_topic_type: str
    category: str


class EmployeeAssignment(BaseModel):
    assigned_task: Optional[str]
    employee_profile: EmployeeProfile


class AgentSelectionResponse(BaseModel):
    agent_found: bool
    matched_employee: Optional[EmployeeAssignment] = None
    message: Optional[str]
    task_assigned: bool


class KnowledgeStructuredResponse(BaseModel):
    content: str
    sources: Optional[List[str]]
    file_names: Optional[List[str]]


class AgentResponse(BaseModel):
    content: str
    content_type: ContentType
