"""
Tests for the updated ModelFactory with comprehensive provider support.
"""

from unittest.mock import Mock, patch

from autogen_core.models import ModelFamily

from app.autogen_service.model_factory import ModelFactory


class TestModelFactory:
    """Test cases for ModelFactory."""

    def test_determine_model_family_openai(self):
        """Test model family determination for OpenAI models."""
        # Test GPT-4o
        assert (
            ModelFactory._determine_model_family("gpt-4o", "openai")
            == ModelFamily.GPT_4O
        )
        assert (
            ModelFactory._determine_model_family("gpt-4o-mini", "openai")
            == ModelFamily.GPT_4O
        )

        # Test GPT-4
        assert (
            ModelFactory._determine_model_family("gpt-4", "openai") == ModelFamily.GPT_4
        )
        assert (
            ModelFactory._determine_model_family("gpt-4-turbo", "openai")
            == ModelFamily.GPT_4
        )

        # Test GPT-3.5
        assert (
            ModelFactory._determine_model_family("gpt-3.5-turbo", "openai")
            == ModelFamily.GPT_35
        )
        assert (
            ModelFactory._determine_model_family("gpt-35-turbo", "openai")
            == ModelFamily.GPT_35
        )

        # Test O1 models
        assert (
            ModelFactory._determine_model_family("o1-preview", "openai")
            == ModelFamily.O1
        )
        assert (
            ModelFactory._determine_model_family("o1-mini", "openai") == ModelFamily.O1
        )

    def test_determine_model_family_anthropic(self):
        """Test model family determination for Anthropic models."""
        # Test Claude 3.5 Sonnet
        assert (
            ModelFactory._determine_model_family(
                "claude-3-5-sonnet-20241022", "anthropic"
            )
            == ModelFamily.CLAUDE_3_5_SONNET
        )

        # Test Claude 3.5 Haiku
        assert (
            ModelFactory._determine_model_family(
                "claude-3-5-haiku-20241022", "anthropic"
            )
            == ModelFamily.CLAUDE_3_5_HAIKU
        )

        # Test Claude 3 Opus
        assert (
            ModelFactory._determine_model_family("claude-3-opus-20240229", "anthropic")
            == ModelFamily.CLAUDE_3_OPUS
        )

        # Test Claude 3 Sonnet
        assert (
            ModelFactory._determine_model_family(
                "claude-3-sonnet-20240229", "anthropic"
            )
            == ModelFamily.CLAUDE_3_SONNET
        )

    def test_determine_model_family_gemini(self):
        """Test model family determination for Gemini models."""
        # Test Gemini 1.5 Flash
        assert (
            ModelFactory._determine_model_family("gemini-1.5-flash", "google")
            == ModelFamily.GEMINI_1_5_FLASH
        )

        # Test Gemini 1.5 Pro
        assert (
            ModelFactory._determine_model_family("gemini-1.5-pro", "google")
            == ModelFamily.GEMINI_1_5_PRO
        )

        # Test Gemini 2.0 Flash
        assert (
            ModelFactory._determine_model_family("gemini-2.0-flash", "google")
            == ModelFamily.GEMINI_2_0_FLASH
        )

    def test_determine_model_family_requesty_format(self):
        """Test model family determination with Requesty provider/model format."""
        # Test OpenAI models with provider prefix
        assert (
            ModelFactory._determine_model_family("openai/gpt-4o", "openai")
            == ModelFamily.GPT_4O
        )

        # Test Anthropic models with provider prefix
        assert (
            ModelFactory._determine_model_family(
                "anthropic/claude-3-sonnet-20240229", "openai"
            )
            == ModelFamily.CLAUDE_3_SONNET
        )

        # Test Google models with provider prefix
        assert (
            ModelFactory._determine_model_family("google/gemini-1.5-flash", "openai")
            == ModelFamily.GEMINI_1_5_FLASH
        )

    def test_supports_vision_openai(self):
        """Test vision support detection for OpenAI models."""
        # Vision-enabled models
        assert ModelFactory._supports_vision("gpt-4o", "openai") == True
        assert ModelFactory._supports_vision("gpt-4-vision-preview", "openai") == True
        assert ModelFactory._supports_vision("gpt-4-turbo", "openai") == True

        # Non-vision models
        assert ModelFactory._supports_vision("gpt-3.5-turbo", "openai") == False
        assert ModelFactory._supports_vision("gpt-4", "openai") == False

    def test_supports_vision_anthropic(self):
        """Test vision support detection for Anthropic models."""
        # Claude 3 and 4 models support vision
        assert (
            ModelFactory._supports_vision("claude-3-sonnet-20240229", "anthropic")
            == True
        )
        assert (
            ModelFactory._supports_vision("claude-3-opus-20240229", "anthropic") == True
        )
        assert ModelFactory._supports_vision("claude-4-sonnet", "anthropic") == True

        # Older models don't support vision
        assert ModelFactory._supports_vision("claude-2.1", "anthropic") == False

    def test_supports_vision_gemini(self):
        """Test vision support detection for Gemini models."""
        # All Gemini models support vision
        assert ModelFactory._supports_vision("gemini-1.5-flash", "google") == True
        assert ModelFactory._supports_vision("gemini-2.0-flash", "google") == True
        assert ModelFactory._supports_vision("gemini-1.5-pro", "gemini") == True

    def test_supports_multiple_system_messages(self):
        """Test multiple system messages support detection."""
        # Most models support multiple system messages
        assert (
            ModelFactory._supports_multiple_system_messages("gpt-4o", "openai") == True
        )
        assert (
            ModelFactory._supports_multiple_system_messages(
                "claude-3-sonnet", "anthropic"
            )
            == True
        )
        assert (
            ModelFactory._supports_multiple_system_messages(
                "gemini-1.5-flash", "google"
            )
            == True
        )

        # O1 models don't support multiple system messages
        assert (
            ModelFactory._supports_multiple_system_messages("o1-preview", "openai")
            == False
        )
        assert (
            ModelFactory._supports_multiple_system_messages("o3-mini", "openai")
            == False
        )

    def test_supports_multiple_system_messages_requesty_format(self):
        """Test multiple system messages support with Requesty format."""
        # Test with provider prefix
        assert (
            ModelFactory._supports_multiple_system_messages("openai/gpt-4o", "openai")
            == True
        )
        assert (
            ModelFactory._supports_multiple_system_messages(
                "openai/o1-preview", "openai"
            )
            == False
        )
        assert (
            ModelFactory._supports_multiple_system_messages(
                "anthropic/claude-3-sonnet", "openai"
            )
            == True
        )

    @patch("app.autogen_service.model_factory.settings")
    def test_create_openai_client_with_requesty_defaults(self, mock_settings):
        """Test OpenAI client creation with Requesty defaults."""
        # Mock settings
        mock_settings.requesty.api_key = "test-requesty-key"
        mock_settings.requesty.base_url = "https://router.requesty.ai/v1"

        config = {
            "provider": "OpenAIChatCompletionClient",
            "llm_type": "openai",
            "model": "gpt-4o-mini",
            "temperature": 0.7,
        }

        with patch(
            "app.autogen_service.model_factory.OpenAIChatCompletionClient"
        ) as mock_client:
            mock_instance = Mock()
            mock_client.return_value = mock_instance

            result = ModelFactory.create_model_client(config)

            # Verify client was created with Requesty defaults
            mock_client.assert_called_once()
            call_args = mock_client.call_args
            assert call_args[1]["api_key"] == "test-requesty-key"
            assert call_args[1]["base_url"] == "https://router.requesty.ai/v1"
            assert call_args[1]["model"] == "gpt-4o-mini"
            assert call_args[1]["temperature"] == 0.7

    @patch("app.autogen_service.model_factory.settings")
    def test_create_openai_client_with_requesty_model_format(self, mock_settings):
        """Test OpenAI client creation with Requesty model format."""
        mock_settings.requesty.api_key = "test-requesty-key"
        mock_settings.requesty.base_url = "https://router.requesty.ai/v1"

        config = {
            "provider": "OpenAIChatCompletionClient",
            "llm_type": "anthropic",
            "model": "claude-3-sonnet-20240229",
            "temperature": 0.5,
        }

        with patch(
            "app.autogen_service.model_factory.OpenAIChatCompletionClient"
        ) as mock_client:
            mock_instance = Mock()
            mock_client.return_value = mock_instance

            result = ModelFactory.create_model_client(config)

            # Verify model was formatted for Requesty
            call_args = mock_client.call_args
            assert call_args[1]["model"] == "anthropic/claude-3-sonnet-20240229"

    def test_create_model_client_missing_model(self):
        """Test error handling when model is missing."""
        config = {
            "provider": "OpenAIChatCompletionClient",
            "llm_type": "openai",
            # Missing model
        }

        result = ModelFactory.create_model_client(config)
        assert result is None

    def test_create_model_client_unsupported_provider(self):
        """Test error handling for unsupported provider."""
        config = {"provider": "UnsupportedProvider", "model": "some-model"}

        result = ModelFactory.create_model_client(config)
        assert result is None

    def test_get_supported_providers(self):
        """Test getting supported providers information."""
        providers = ModelFactory.get_supported_providers()

        # Check that all expected providers are present
        expected_providers = [
            "OpenAIChatCompletionClient",
            "AnthropicChatCompletionClient",
            "AzureOpenAIChatCompletionClient",
            "AzureAIChatCompletionClient",
            "OllamaChatCompletionClient",
        ]

        for provider in expected_providers:
            assert provider in providers
            assert "description" in providers[provider]
            assert "supports_requesty" in providers[provider]
            assert "example_models" in providers[provider]
            assert "required_config" in providers[provider]
            assert "optional_config" in providers[provider]

    def test_create_model_info_validation(self):
        """Test model info creation and validation."""
        with patch(
            "app.autogen_service.model_factory.validate_model_info"
        ) as mock_validate:
            # Test successful validation
            mock_validate.return_value = None

            model_info = ModelFactory._create_model_info(
                "gpt-4o", "openai", {}, ModelFamily.GPT_4O
            )

            assert model_info["family"] == ModelFamily.GPT_4O
            assert model_info["vision"] == True  # gpt-4o supports vision
            assert model_info["function_calling"] == True
            assert model_info["multiple_system_messages"] == True

            mock_validate.assert_called_once_with(model_info)

    def test_create_model_info_validation_failure(self):
        """Test model info creation with validation failure."""
        with patch(
            "app.autogen_service.model_factory.validate_model_info"
        ) as mock_validate:
            # Test validation failure
            mock_validate.side_effect = Exception("Validation failed")

            model_info = ModelFactory._create_model_info(
                "gpt-4o", "openai", {}, ModelFamily.GPT_4O
            )

            # Should still return model info with safe defaults
            assert model_info["multiple_system_messages"] == True
            mock_validate.assert_called_once_with(model_info)
