[{"id": "47bcec7d-91d2-433b-91a8-98d0e7d1ce89", "name": "Email Employee", "description": "You are <PERSON><PERSON><PERSON>, an autonomous agent responsible for sending emails using the `send_email` tool. You take user instructions and convert them into a proper email format, then call the tool with the following parameters:\n\n- `to`: Recipient's email address (required)\n- `subject`: Email subject line (required)\n- `body`: Full content of the email (required)\n\nEnsure the message is clear, concise, and appropriate for the context. Never fabricate or assume any missing fields—ask the user for missing inf", "avatar": "https://storage.googleapis.com/ruh-dev/agent-avatars/1747662398-Clippathgroup-9.svg", "department": "marketing", "category": "general", "tags": null, "created_at": "2025-07-01T12:19:09.253757", "updated_at": "2025-07-01T12:20:19.101668", "owner_id": "fce79072-a235-4127-ac5b-b5b1709a8077", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "use_count": 1, "visibility": "public", "workflow_ids": null, "mcp_server_ids": ["af3db8c7-a9c8-428c-8f21-db54da1c0d82"], "agent_topic_type": "Professional Em<PERSON> and Summarizer", "is_a2a": null, "is_customizable": null, "agent_capabilities": {"capabilities": null, "input_modes": null, "output_modes": null, "response_model": null, "id": "aedc8113-92a2-4ad2-ba41-b2b2f8758b7c", "created_at": "2025-06-27T14:36:35.134955", "updated_at": "2025-06-27T14:36:35.134958"}, "example_prompts": null, "is_added": true, "agent_category": "ai_agent", "system_message": "You are <PERSON><PERSON><PERSON>, an autonomous agent responsible for sending emails using the `send_email` tool. You take user instructions and convert them into a proper email format, then call the tool with the following parameters:\n\n- `to`: Recipient's email address (required)\n- `subject`: Email subject line (required)\n- `body`: Full content of the email (required)\n\nEnsure the message is clear, concise, and appropriate for the context. Never fabricate or assume any missing fields—ask the user for missing information before proceeding.\n\nYou must only send the email when all three parameters (`to`, `subject`, and `body`) are available and confirmed.\n", "model_provider": "openai", "model_name": "gpt-4o", "max_tokens": null, "temperature": null, "subscriptions": null, "tone": "professional", "files": null, "urls": null, "status": "active", "capabilities_id": "aedc8113-92a2-4ad2-ba41-b2b2f8758b7c", "source_workflow_id": null, "source_agent_id": "8f6b433a-581b-4087-b48f-9bdc72a9a539", "version": "1.1.0", "mcps": [{"id": "af3db8c7-a9c8-428c-8f21-db54da1c0d82", "name": "<PERSON><PERSON><PERSON>", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/sendgrid.png/**********-sendgrid.png", "description": "The SendGrid Email MCP Server is an AI-compatible service that allows agents or users to send emails using the SendGrid API. It supports dynamic templates, HTML/plain text emails. ", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "user_ids": null, "owner_type": "user", "config": [{"url": "https://sendgrid-mcp-dev-624209391722.us-central1.run.app/mcp", "type": "streamable-http"}], "git_url": null, "git_branch": null, "deployment_status": "pending", "visibility": "public", "tags": null, "status": "active", "created_at": "2025-06-18T06:09:32.672769", "updated_at": "2025-07-01T12:20:19.032243", "image_name": null, "category": "general", "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "send_mail", "description": "Send mail to the user using SendGrid", "input_schema": {"properties": {"to": {"title": "To", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}}, "required": ["to", "subject", "body"], "title": "SendMail", "type": "object"}, "output_schema": {"properties": {"success": {"type": "boolean", "description": "Indicates whether the email was sent successfully", "title": "success"}, "status_code": {"type": "integer", "description": "HTTP status code returned by SendGrid", "title": "status_code"}, "message_id": {"type": "string", "description": "Unique identifier for the sent message provided by SendGrid", "title": "message_id"}, "status": {"type": "string", "description": "Human-readable status message", "title": "status"}, "timestamp": {"type": "string", "format": "date-time", "description": "UTC timestamp when the email was processed", "title": "timestamp"}}}, "annotations": null}, {"name": "send_bulk_mail", "description": "Send bulk mail to multiple recipients using SendGrid", "input_schema": {"properties": {"recipients": {"items": {"type": "string"}, "title": "Recipients", "type": "array"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}}, "required": ["recipients", "subject", "body"], "title": "SendBulkMail", "type": "object"}, "output_schema": {"properties": {"url": {"type": "string", "description": "url link", "title": "url"}}}, "annotations": null}]}, "is_added": false, "env_keys": null, "component_category": "notifications alerts", "env_credential_status": null, "oauth_details": null}], "workflows": []}, {"id": "6bedaf89-1094-42e3-8ca0-69f08af57ee5", "name": "SlideDeckGenerator", "description": "You are SlideDeckGenerator — an expert AI presentation builder focused on transforming structured slide-ready data into a professional marketing pitch deck. Your role is to finalize and deliver a complete PowerPoint presentation based on pre-formatted slide content provided by upstream agents.\n\n", "avatar": "https://storage.googleapis.com/ruh-dev/agent-avatars/1747662426-Clippathgroup-10.svg", "department": "IT", "category": "marketing", "tags": null, "created_at": "2025-07-01T12:21:21.851318", "updated_at": "2025-07-01T12:21:21.851322", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "owner_name": "<PERSON><PERSON>", "average_rating": null, "use_count": null, "visibility": "public", "workflow_ids": ["87abd5f7-1044-46dc-98bd-f274b529323d"], "mcp_server_ids": null, "agent_topic_type": "Marketing Agent", "is_a2a": null, "is_customizable": null, "agent_capabilities": {"capabilities": null, "input_modes": ["text"], "output_modes": ["text"], "response_model": null, "id": "72d18fc3-fcb0-4cb0-b20a-58bdfcb23c6d", "created_at": "2025-07-01T12:20:31.936393", "updated_at": "2025-07-01T12:20:31.936397"}, "example_prompts": null, "is_added": false, "agent_category": "ai_agent", "system_message": "You are Slide<PERSON>eck<PERSON>enerator, an expert AI designed to create polished, professional marketing pitch decks from structured, slide-ready data provided by upstream agents. Your primary task is to assemble, refine, and deliver a complete PowerPoint presentation that accurately reflects the provided content. \n\nGuidelines:\n• Ensure all slides are coherent, visually appealing, and logically organized.\n• Maintain consistency in style, formatting, fonts, and color schemes.\n• Verify that slide content is accurate, concise, and persuasive.\n• Do not add or invent any content beyond the provided data.\n• Limit your output to a final, ready-to-present PowerPoint file; do not include extraneous commentary or draft notes.\n• Follow best practices for professional presentations, emphasizing clarity and impact.\n• If any information is missing or unclear, request clarification before proceeding.", "model_provider": "openai", "model_name": "gpt-4o", "max_tokens": 3000, "temperature": 0.7, "subscriptions": null, "tone": "professional", "files": null, "urls": null, "status": "active", "capabilities_id": "72d18fc3-fcb0-4cb0-b20a-58bdfcb23c6d", "source_workflow_id": null, "source_agent_id": "a82d18c2-ad94-4553-81a8-68c313308c1f", "version": "1.0.0", "mcps": [], "workflows": [{"id": "87abd5f7-1044-46dc-98bd-f274b529323d", "name": "PPT Generation With Slides", "description": "PPT_Generation_With_Slides", "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/dbd54d90-a092-4f51-9564-3a7b5cefb5e1.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/f3958b0b-ac3c-4e9c-80bd-baaa8784b7bc.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1751355542226"}, {"field": "input_variables", "type": "dict", "transition_id": "transition-AgenticAI-1751355542226"}], "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "user_ids": ["91a237fd-0225-4e02-9e9f-805eff073b07"], "owner_type": "user", "workflow_template_id": null, "template_owner_id": null, "is_imported": false, "version": "1.1.0", "visibility": "public", "category": null, "tags": null, "status": "active", "is_changes_marketplace": false, "is_customizable": true, "auto_version_on_update": false, "created_at": "2025-06-27T08:00:48.397801", "updated_at": "2025-07-01T12:15:24.554689", "available_nodes": [{"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1751355540991", "label": "PresentationTemplateSelector"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1751355542226", "label": "PresentationContentArchitect"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1751355543490", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1751363433813", "label": "SlideDeckGenerator"}], "is_updated": false}]}, {"id": "a61baa97-7078-4668-84ee-9d05a69ac61e", "name": "<PERSON><PERSON><PERSON>", "description": "Ciny is an AI-powered video generation assistant that helps users create high-quality video content with ease. It provides expert guidance on video concepts, editing, and multimedia integration to ensure professional results.", "avatar": null, "department": "IT", "category": "general", "tags": null, "created_at": "2025-07-01T13:51:57.290746", "updated_at": "2025-07-01T13:58:06.165005", "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "use_count": 2, "visibility": "public", "workflow_ids": ["5ab054b8-1a14-462c-bbda-3d41bbce0b5a"], "mcp_server_ids": null, "agent_topic_type": "video generation ", "is_a2a": null, "is_customizable": null, "agent_capabilities": {"capabilities": [{"title": "video generation ", "description": "Advanced AI assistant specialized in video generation"}], "input_modes": ["text"], "output_modes": ["text"], "response_model": null, "id": "2e19f4d2-565f-4478-81b5-e4426c6e09c9", "created_at": "2025-07-01T13:51:06.279213", "updated_at": "2025-07-01T13:51:06.279216"}, "example_prompts": null, "is_added": false, "agent_category": "ai_agent", "system_message": "You are <PERSON><PERSON><PERSON>, an advanced AI assistant specialized in video generation. Your primary role is to assist users in creating video content.\nGuidelines:\n• Focus on providing clear, concise, and actionable responses related to video generation tasks.\n• Support users by offering suggestions for video concepts, editing techniques, and multimedia integration.\n• Adhere to best practices in video production, ensuring high-quality outputs and user satisfaction.\n• When answering questions or providing assistance, prioritize clarity and accuracy.\n• Maintain user privacy and security, avoiding the use of sensitive or personal data.\nConstraints:\n• Do not generate or manipulate video content that is inappropriate, harmful, or violates any ethical standards.\n• Avoid giving legal, medical, or financial advice.\n", "model_provider": "openai", "model_name": "gpt-4o-mini", "max_tokens": 2048, "temperature": 0.7, "subscriptions": null, "tone": "professional", "files": null, "urls": null, "status": "active", "capabilities_id": "2e19f4d2-565f-4478-81b5-e4426c6e09c9", "source_workflow_id": null, "source_agent_id": "d1e8a246-a56c-40ce-9108-075da526d576", "version": "1.0.0", "mcps": [], "workflows": [{"id": "5ab054b8-1a14-462c-bbda-3d41bbce0b5a", "name": "Video Generation -<PERSON><PERSON><PERSON>", "description": "Video_Generation_-<PERSON><PERSON><PERSON>", "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/0cecb9c0-e7f0-4f32-ba87-a1a51c7f58ab.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/37006c7a-8179-4854-ab5e-9736ee59c144.json", "start_nodes": [{"field": "topic", "type": "string", "transition_id": "transition-MCP_script-generation-mcp_script_generate-1751354608367"}], "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "user_ids": ["c1454e90-09ac-40f2-bde2-833387d7b645"], "owner_type": "user", "workflow_template_id": null, "template_owner_id": null, "is_imported": false, "version": "1.3.0", "visibility": "public", "category": null, "tags": null, "status": "active", "is_changes_marketplace": false, "is_customizable": true, "auto_version_on_update": false, "created_at": "2025-06-27T13:55:15.932203", "updated_at": "2025-07-01T13:50:16.724873", "available_nodes": [{"name": "MCP_Voice_generation_generate_audio", "id": "ad939230-0310-426f-b9e9-1d22fb202eb0", "transition_id": "transition-MCP_Voice_generation_generate_audio-1751032546624", "type": "mcp", "display_name": "Voice generation - generate_audio", "label": "Voice generation - generate_audio"}, {"name": "MCP_Voice_generation_fetch_audio", "id": "ad939230-0310-426f-b9e9-1d22fb202eb0", "transition_id": "transition-MCP_Voice_generation_fetch_audio-1751032559890", "type": "mcp", "display_name": "Voice generation - fetch_audio", "label": "Voice generation - fetch_audio"}, {"name": "MCP_content-extractor-mcp_generate_subtitle", "id": "9e0144c3-39c8-46bd-a7f7-47925756e78a", "transition_id": "transition-MCP_content-extractor-mcp_generate_subtitle-1751032590340", "type": "mcp", "display_name": "content-extractor-mcp - generate_subtitle", "label": "content-extractor-mcp - generate_subtitle"}, {"name": "MCP_Stock_Video_Generation_generate_stock_video", "id": "8f4dafe4-0682-4476-b342-b3ff1b9c98fa", "transition_id": "transition-MCP_Stock_Video_Generation_generate_stock_video-1751032649990", "type": "mcp", "display_name": "Stock Video Generation - generate_stock_video", "label": "Stock Video Generation - generate_stock_video"}, {"name": "MCP_video-generation-mcp_generate_video", "id": "56dfe8af-e982-4351-a669-0a03755b8c99", "transition_id": "transition-MCP_video-generation-mcp_generate_video-1751289611591", "type": "mcp", "display_name": "video-generation-mcp - generate_video", "label": "video-generation-mcp - generate_video"}, {"name": "MCP_stock-image-generation-mcp_generate_ai_stock_image", "id": "cde76df3-a879-496a-95f4-8b1f95d81a12", "transition_id": "transition-MCP_stock-image-generation-mcp_generate_ai_stock_image-1751294035428", "type": "mcp", "display_name": "stock-image-generation-mcp - generate_ai_stock_image", "label": "stock-image-generation-mcp - generate_ai_stock_image"}, {"name": "MCP_script-generation-mcp_script_generate", "id": "748a8221-d7d9-4352-93ae-00700f4d28b1", "transition_id": "transition-MCP_script-generation-mcp_script_generate-1751354608367", "type": "mcp", "display_name": "script-generation-mcp - script_generate", "label": "script-generation-mcp - script_generate"}], "is_updated": true}]}]