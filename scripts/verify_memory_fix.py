#!/usr/bin/env python3
"""
Quick verification script for the Pinecone memory update_context fix.

This script simulates the exact scenario that was causing the error
and verifies that it's now fixed.
"""

import asyncio
import logging
import os
import sys

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

from autogen_core.model_context import BufferedChatCompletionContext

from app.autogen_service.agent_factory import AgentFactory
from app.memory import PineconeMemory
from app.schemas.api import AgentConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def simulate_original_error():
    """Simulate the original error scenario and verify it's fixed."""
    try:
        logger.info("🔍 Simulating the original error scenario...")

        # Create agent configuration
        agent_config = AgentConfig(
            id="test-agent-error-fix",
            name="TestAgent",
            description="Test agent for error fix verification",
            system_message="You are a helpful AI assistant.",
            model_provider="openai",
            model_name="gpt-4o-mini",
            workflows=[],
            mcps=[],
        )

        # Create agent factory
        agent_factory = AgentFactory()

        # Create model context
        model_context = BufferedChatCompletionContext(buffer_size=8)

        logger.info("📝 Creating agent with Pinecone memory...")

        # Create agent with Pinecone memory (this should work now)
        agent = await agent_factory.create_agent(
            run_id="test-run-fix",
            agent_config=agent_config,
            model_context=model_context,
            memory=None,
            organization_id="test-org",
            use_knowledge=False,
            variables=None,
            user_id="test-user",
            use_pinecone_memory=True,
        )

        logger.info("✅ Agent created successfully with Pinecone memory!")

        # Verify agent has memory
        if agent.memory and len(agent.memory) > 0:
            logger.info(f"✅ Agent has {len(agent.memory)} memory instance(s)")

            # Check if any memory is PineconeMemory
            pinecone_memory_found = False
            for mem in agent.memory:
                if isinstance(mem, PineconeMemory):
                    pinecone_memory_found = True
                    logger.info("✅ PineconeMemory instance found in agent memory")

                    # Test the update_context method directly
                    logger.info("🧪 Testing update_context method...")

                    # Add a test message to context
                    from autogen_agentchat.messages import UserMessage

                    test_message = UserMessage(
                        content="Test message for memory", source="user"
                    )
                    await model_context.add_message(test_message)

                    # This should not raise an error anymore
                    await mem.update_context(model_context)
                    logger.info("✅ update_context method executed successfully!")

                    break

            if not pinecone_memory_found:
                logger.warning("⚠️ No PineconeMemory instance found in agent memory")
                return False
        else:
            logger.warning("⚠️ Agent has no memory instances")
            return False

        logger.info("🎉 Original error scenario test PASSED!")
        return True

    except TypeError as e:
        if "missing 1 required positional argument: 'query'" in str(e):
            logger.error("❌ Original error still exists!")
            logger.error(f"Error: {e}")
            return False
        else:
            logger.error(f"❌ Different TypeError occurred: {e}")
            return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        return False


async def test_memory_method_signatures():
    """Test that all memory methods have correct signatures."""
    try:
        logger.info("🔍 Testing memory method signatures...")

        # Create a PineconeMemory instance
        memory = PineconeMemory(
            agent_id="test-signatures", user_id="test-user", namespace="test-signatures"
        )

        import inspect

        # Test update_context signature
        update_context_sig = inspect.signature(memory.update_context)
        params = list(update_context_sig.parameters.keys())

        # Remove 'self' parameter
        params = [p for p in params if p != "self"]

        if len(params) == 1 and params[0] == "context":
            logger.info("✅ update_context has correct signature: (context)")
        else:
            logger.error(f"❌ update_context has incorrect signature: {params}")
            return False

        # Test other method signatures
        methods_to_check = {
            "add": ["memory"],
            "query": ["query", "k"],
            "clear": [],
        }

        for method_name, expected_params in methods_to_check.items():
            if hasattr(memory, method_name):
                method_sig = inspect.signature(getattr(memory, method_name))
                actual_params = [p for p in method_sig.parameters.keys() if p != "self"]

                # For methods with optional parameters, check minimum required
                if method_name == "query":
                    # query method has 'query' required and 'k' optional
                    if len(actual_params) >= 1 and actual_params[0] == "query":
                        logger.info(f"✅ {method_name} has correct signature")
                    else:
                        logger.error(
                            f"❌ {method_name} has incorrect signature: {actual_params}"
                        )
                        return False
                else:
                    # Check exact match for other methods
                    if actual_params == expected_params:
                        logger.info(f"✅ {method_name} has correct signature")
                    else:
                        logger.error(
                            f"❌ {method_name} has incorrect signature: {actual_params}"
                        )
                        return False

        await memory.close()
        logger.info("✅ All method signatures are correct!")
        return True

    except Exception as e:
        logger.error(f"❌ Method signature test failed: {e}")
        return False


async def main():
    """Main verification function."""
    logger.info("=" * 70)
    logger.info("VERIFYING PINECONE MEMORY UPDATE_CONTEXT FIX")
    logger.info("=" * 70)

    tests = [
        ("Method Signatures", test_memory_method_signatures),
        ("Original Error Scenario", simulate_original_error),
    ]

    results = {}

    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"Test '{test_name}': {status}")
        except Exception as e:
            logger.error(f"Test '{test_name}' crashed: {e}")
            results[test_name] = False

    # Summary
    logger.info("\n" + "=" * 70)
    logger.info("VERIFICATION SUMMARY")
    logger.info("=" * 70)

    passed = sum(1 for result in results.values() if result)
    total = len(results)

    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")

    logger.info(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        logger.info("\n🎉 SUCCESS! The Pinecone memory fix is working correctly.")
        logger.info(
            "The original error 'missing 1 required positional argument: query' should be resolved."
        )
        return True
    else:
        logger.error("\n❌ FAILURE! Some tests failed. The fix may not be complete.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
