import asyncio
import logging
import json
import uuid
from typing import Dict, Any
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer  # type: ignore
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


KAFKA_BOOTSTRAP_SERVERS = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "34.172.106.233:9092")
KAFKA_AGENT_QUERY_TOPIC = os.getenv("KAFKA_AGENT_QUERY_TOPIC", "agent_query_requests")
KAFKA_AGENT_RESPONSE_TOPIC = os.getenv(
    "KAFKA_AGENT_RESPONSE_TOPIC", "agent_chat_responses"
)

print(f"KAFKA_BOOTSTRAP_SERVERS: {KAFKA_BOOTSTRAP_SERVERS}")
print(f"KAFKA_AGENT_QUERY_TOPIC: {KAFKA_AGENT_QUERY_TOPIC}")
print(f"KAFKA_AGENT_RESPONSE_TOPIC: {KAFKA_AGENT_RESPONSE_TOPIC}")


class AgentQueryTestClient:
    def __init__(self):
        self.producer = None
        self.consumer = None
        self._initialized = False

    async def initialize(self):
        if self._initialized:
            return

        self.producer = AIOKafkaProducer(
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            max_request_size=524288000,
        )
        self.consumer = AIOKafkaConsumer(
            KAFKA_AGENT_RESPONSE_TOPIC,
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            group_id=f"test-query-group-{uuid.uuid4()}",
            auto_offset_reset="latest",
            enable_auto_commit=True,
        )

        await self.producer.start()
        await self.consumer.start()
        self._initialized = True
        logger.info("Agent Query test client initialized successfully")

    async def cleanup(self):
        if self.producer:
            await self.producer.stop()
        if self.consumer:
            await self.consumer.stop()

    async def send_message(
        self, topic: str, message: Dict[str, Any], headers: list = None
    ) -> None:
        try:
            value = json.dumps(message).encode("utf-8")
            await self.producer.send_and_wait(topic, value=value, headers=headers)
            logger.info(f"Message sent to topic {topic}: {message}")
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise

    async def listen_for_responses(self, correlation_id: str = None, timeout: int = 30):
        """Listen for responses with improved debugging and error handling"""
        try:
            start_time = asyncio.get_event_loop().time()
            responses = []
            logger.info(
                f"Starting to listen for responses with correlation_id: {correlation_id}"
            )

            while True:
                try:
                    # Set a timeout for each message poll
                    msg = await asyncio.wait_for(
                        self.consumer.getone(), timeout=timeout
                    )

                    current_time = asyncio.get_event_loop().time()
                    if current_time - start_time > timeout:
                        logger.warning("Response listening timeout reached")
                        break

                    # Log raw message for debugging
                    logger.debug(f"Received raw message: {msg}")

                    # Process message headers
                    msg_correlation_id = None
                    if msg.headers:
                        headers_dict = {
                            k: v.decode("utf-8") if isinstance(v, bytes) else v
                            for k, v in msg.headers
                        }
                        msg_correlation_id = headers_dict.get("correlationId")
                        logger.debug(f"Message headers: {headers_dict}")

                    # Decode and process message value
                    try:
                        response = json.loads(msg.value.decode("utf-8"))
                        logger.debug(f"Decoded response: {response}")

                        # Check correlation ID match
                        if correlation_id and msg_correlation_id == correlation_id:
                            logger.info(
                                f"Matched response for correlation_id {correlation_id}"
                            )
                            responses.append(response)

                            # Check if this is a final response
                            if response.get("final", True):
                                logger.info("Received final response, breaking loop")
                                break

                        else:
                            logger.debug(
                                f"Skipping message with different correlation_id: {msg_correlation_id}"
                            )

                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to decode message value: {e}")
                        continue

                except asyncio.TimeoutError:
                    logger.warning("Timeout waiting for message")
                    break
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    continue

            logger.info(f"Finished listening. Collected {len(responses)} responses")
            return responses

        except Exception as e:
            logger.error(f"Error in listen_for_responses: {e}")
            raise


async def test_agent_query():
    """Test agent query functionality"""
    client = AgentQueryTestClient()
    await client.initialize()

    try:
        print("\nAgent Query Test")
        print("=" * 50)

        # Get user input for the query
        query = input("Enter your query for the agent: ").strip()
        if not query:
            query = "Hello, can you help me with a simple task?"

        # Generate unique run ID
        run_id = str(uuid.uuid4())

        # Prepare agent query request
        query_request = {
            "agent_id": "26b1fda5-2e28-403d-8cf8-8c08e03436c6",  # Default agent ID
            "query": [query, "how you help me"],
            "run_id": run_id,
            "user_id": "test_user",
            "organization_id": None,
            "variables": {},
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        logger.info(f"Sending agent query with run_id: {run_id}")
        logger.info(f"Query: {query}")

        # Send query request
        await client.send_message(KAFKA_AGENT_QUERY_TOPIC, query_request, headers)

        # Wait for response
        responses = await client.listen_for_responses(correlation_id=run_id, timeout=60)

        if not responses:
            print("❌ No response received for agent query")
            return

        print(f"\n✅ Received {len(responses)} response(s)")
        print("-" * 50)

        for i, response in enumerate(responses, 1):
            print(f"\nResponse {i}:")
            print(f"Success: {response.get('success', False)}")
            print(f"Message: {response.get('message', 'No message')}")

            agent_response = response.get("agent_response", {})
            if agent_response:
                content = agent_response.get("content", "No content")
                print(f"Agent Response: {content}")

            print(f"Final: {response.get('final', False)}")

    except Exception as e:
        logger.error(f"Agent query test error: {e}")
        print(f"❌ Test failed: {e}")
    finally:
        await client.cleanup()


async def interactive_agent_query():
    """Interactive agent query session"""
    client = AgentQueryTestClient()
    await client.initialize()

    try:
        print("\nInteractive Agent Query Session")
        print("Type 'quit' to exit")
        print("=" * 50)

        while True:
            # Get user input
            query = input("\nYour query: ").strip()
            if query.lower() in ["quit", "exit", "bye"]:
                break

            if not query:
                continue

            # Generate unique run ID for each query
            run_id = str(uuid.uuid4())

            # Prepare agent query request
            query_request = {
                "agent_id": "26b1fda5-2e28-403d-8cf8-8c08e03436c6",
                "query": [query, "how you help me"],
                "run_id": run_id,
                "user_id": "test_user",
                "organization_id": None,
                "variables": {},
            }

            headers = [
                ("correlationId", run_id.encode("utf-8")),
                ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
            ]

            # Send query request
            await client.send_message(KAFKA_AGENT_QUERY_TOPIC, query_request, headers)

            # Wait for and process responses
            responses = await client.listen_for_responses(
                correlation_id=run_id, timeout=30
            )

            if responses:
                for response in responses:
                    if response.get("success", False):
                        agent_response = response.get("agent_response", {})
                        content = agent_response.get("content", "No content available")
                        print(f"\n🤖 Agent: {content}")
                    else:
                        print(f"\n❌ Error: {response.get('message', 'Unknown error')}")
            else:
                print("\n❌ No response received from agent")

    except Exception as e:
        logger.error(f"Interactive query error: {e}")
        print(f"❌ Session error: {e}")
    finally:
        await client.cleanup()


async def main():
    try:
        print("Agent Query Test Options:")
        print("1. Single query test")
        print("2. Interactive query session")

        choice = input("\nSelect option (1 or 2): ").strip()

        if choice == "1":
            await test_agent_query()
        elif choice == "2":
            await interactive_agent_query()
        else:
            print("Invalid choice. Running single query test...")
            await test_agent_query()

    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
