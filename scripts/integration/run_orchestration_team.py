#!/usr/bin/env python3
"""
Interactive chat script for orchestration team with multimodal support and human-in-the-loop functionality.
This script provides:
1. Interactive chat with the global agent orchestration team
2. Multimodal input support (images, documents)
3. Human-in-the-loop interaction handling
4. Real-time streaming responses
5. Session management
"""

import asyncio
import base64
import json
import logging
import os
import sys
import uuid
from typing import Any, Dict, List

from aiokafka import AIOKafkaConsumer, AIOKafkaProducer  # type: ignore

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Kafka configuration
KAFKA_BOOTSTRAP_SERVERS = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "**************:9092")
KAFKA_ORCHESTRATION_TEAM_SESSION_TOPIC = os.getenv(
    "KAFKA_ORCHESTRATION_TEAM_SESSION_TOPIC", "orchestration_team_session_requests"
)
KAFKA_ORCHESTRATION_TEAM_CHAT_TOPIC = os.getenv(
    "KAFKA_ORCHESTRATION_TEAM_CHAT_TOPIC", "orchestration_team_chat_requests"
)
KAFKA_HUMAN_INPUT_RESPONSE_TOPIC = os.getenv(
    "KAFKA_HUMAN_INPUT_RESPONSE_TOPIC", "human_input_responses"
)
KAFKA_AGENT_RESPONSE_TOPIC = os.getenv(
    "KAFKA_AGENT_RESPONSE_TOPIC", "agent_chat_responses"
)
KAFKA_HUMAN_INPUT_REQUEST_TOPIC = os.getenv(
    "KAFKA_HUMAN_INPUT_REQUEST_TOPIC", "human_input_requests"
)


class KafkaTestClient:
    """Kafka client for testing orchestration team functionality"""

    def __init__(self):
        self.producer = None
        self.consumer = None
        self._initialized = False

    async def initialize(self):
        """Initialize Kafka producer and consumer"""
        if self._initialized:
            return

        self.producer = AIOKafkaProducer(
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            max_request_size=524288000,
            value_serializer=lambda v: json.dumps(v).encode("utf-8"),
        )
        self.consumer = AIOKafkaConsumer(
            KAFKA_AGENT_RESPONSE_TOPIC,
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            group_id=f"test-group-{uuid.uuid4()}",
            auto_offset_reset="latest",
            enable_auto_commit=True,
        )

        await self.producer.start()
        await self.consumer.start()
        self._initialized = True
        logger.info("Kafka test client initialized successfully")

    async def cleanup(self):
        """Cleanup Kafka connections"""
        if self.producer:
            await self.producer.stop()
        if self.consumer:
            await self.consumer.stop()

    async def send_message(
        self, topic: str, message: Dict[str, Any], headers: List[tuple] = None
    ):
        """Send message to Kafka topic"""
        if not self.producer:
            raise RuntimeError("Producer not initialized")

        await self.producer.send(topic, message, headers=headers)
        logger.info(f"Message sent to topic: {topic}")

    async def listen_for_responses(
        self, correlation_id: str, timeout: int = 30
    ) -> List[Dict[str, Any]]:
        """Listen for responses with correlation ID"""
        if not self.consumer:
            raise RuntimeError("Consumer not initialized")

        responses = []
        start_time = asyncio.get_event_loop().time()

        try:
            while True:
                current_time = asyncio.get_event_loop().time()
                if current_time - start_time > timeout:
                    logger.warning(
                        f"Timeout reached while waiting for responses (correlation_id: {correlation_id})"
                    )
                    break

                msg_pack = await self.consumer.getmany(timeout_ms=1000)

                for topic_partition, messages in msg_pack.items():
                    for message in messages:
                        try:
                            # Check correlation ID in headers
                            correlation_header = None
                            if message.headers:
                                for key, value in message.headers:
                                    if key == "correlationId":
                                        correlation_header = value.decode("utf-8")
                                        break

                            if correlation_header == correlation_id:
                                message_data = json.loads(message.value.decode("utf-8"))
                                responses.append(message_data)
                                logger.info(
                                    f"Received response for correlation_id: {correlation_id}"
                                )

                                # Check if this is a final response
                                if message_data.get("final", False):
                                    return responses

                        except Exception as e:
                            logger.error(f"Error processing message: {e}")

        except Exception as e:
            logger.error(f"Error in response listening: {e}")

        return responses


def create_sample_image_attachment(use_url: bool = True) -> Dict[str, Any]:
    """Create a sample image attachment for testing"""
    if use_url:
        return {
            "file_name": "sample_image.jpeg",
            "file_type": "image/jpeg",
            "file_size": 0,  # Size will be determined when downloaded
            "file_url": "https://images.pexels.com/photos/30091614/pexels-photo-30091614.jpeg",
            "metadata": {
                "description": "Sample image for multimodal orchestration test"
            },
        }
    else:
        # Create a simple base64 encoded image (1x1 pixel red PNG)
        red_pixel_png = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="

        return {
            "file_name": "red_pixel.png",
            "file_type": "image/png",
            "file_size": len(base64.b64decode(red_pixel_png)),
            "file_data": red_pixel_png,
            "metadata": {"description": "A simple 1x1 red pixel for testing"},
        }


def create_sample_text_attachment() -> Dict[str, Any]:
    """Create a sample text document attachment for testing"""
    text_content = """
# Orchestration Team Test Document

This document is used to test the multimodal capabilities of the orchestration team.

## Key Features Being Tested:
- Image processing and analysis
- Document understanding and summarization
- Human-in-the-loop interaction
- Global agent coordination

## Expected Behavior:
1. The orchestrator should analyze the query and determine which specialist agents to engage
2. Agents should be able to process both the text query and attached files
3. Human input should be requested when clarification is needed
4. Final response should synthesize information from all sources

This is a comprehensive test of the multimodal orchestration system.
"""

    base64_data = base64.b64encode(text_content.encode("utf-8")).decode("utf-8")

    return {
        "file_name": "orchestration_test_doc.md",
        "file_type": "text/markdown",
        "file_size": len(text_content.encode("utf-8")),
        "file_data": base64_data,
        "metadata": {"description": "Test document for multimodal orchestration"},
    }


def parse_multimodal_input(user_input: str) -> tuple[str, List[Dict[str, Any]]]:
    """
    Parse user input for multimodal commands and return message and attachments.

    Commands:
    - /image - Add sample image attachment
    - /doc - Add sample document attachment
    - /both - Add both image and document attachments
    """
    attachments = []
    message = user_input

    if "/image" in user_input:
        attachments.append(create_sample_image_attachment())
        message = user_input.replace("/image", "").strip()
        if not message:
            message = "Please analyze the attached image."

    if "/doc" in user_input:
        attachments.append(create_sample_text_attachment())
        message = user_input.replace("/doc", "").strip()
        if not message:
            message = "Please summarize the attached document."

    if "/both" in user_input:
        attachments.append(create_sample_image_attachment())
        attachments.append(create_sample_text_attachment())
        message = user_input.replace("/both", "").strip()
        if not message:
            message = "Please analyze the attached image and document."

    return message, attachments


class OrchestrationTeamTester:
    """Test orchestration team functionality with human-in-the-loop"""

    def __init__(self, session_id: str = None):
        self.producer = None
        self.consumer = None
        self.session_id = (
            session_id  # Can be provided or will be set when session is created
        )
        self.run_id = str(uuid.uuid4())
        self.pending_human_inputs = {}

    async def setup_kafka(self):
        """Setup Kafka producer and consumer"""
        # Setup producer
        self.producer = AIOKafkaProducer(
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            value_serializer=lambda v: json.dumps(v).encode("utf-8"),
        )
        await self.producer.start()

        # Setup consumer for responses and human input requests
        self.consumer = AIOKafkaConsumer(
            KAFKA_AGENT_RESPONSE_TOPIC,
            KAFKA_HUMAN_INPUT_REQUEST_TOPIC,
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            group_id=f"orchestration_test_{uuid.uuid4()}",
            auto_offset_reset="latest",
        )
        await self.consumer.start()

        logger.info("Kafka producer and consumer setup complete")

    async def cleanup_kafka(self):
        """Cleanup Kafka connections"""
        if self.producer:
            await self.producer.stop()
        if self.consumer:
            await self.consumer.stop()

    async def send_orchestration_team_message(
        self,
        message: str,
        session_id: str = None,
        attachments: List[Dict[str, Any]] = None,
    ):
        """Send a message to the orchestration team with optional attachments"""
        if attachments is None:
            attachments = []

        request = {
            "run_id": self.run_id,
            "session_id": session_id,  # Can be None for auto-creation
            "user_id": "test_user",
            "user_message": message,
            "organization_id": "test_org",
            "variables": {},
            "attachments": attachments,
        }

        headers = [
            ("correlationId", self.run_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        await self.producer.send(
            KAFKA_ORCHESTRATION_TEAM_CHAT_TOPIC,
            request,
            headers=headers,
        )

        # Log attachment info if present
        if attachments:
            attachment_summary = ", ".join(
                [f"{att['file_name']} ({att['file_type']})" for att in attachments]
            )
            logger.info(f"Sending message with attachments: {attachment_summary}")

        if session_id:
            logger.info(f"Sent message to session {session_id}: {message[:50]}...")
        else:
            logger.info(f"Sent message (will create new session): {message[:50]}...")

    async def send_human_input_response(
        self, team_conversation_id: str, user_input: str
    ):
        """Send human input response"""
        response = {
            "session_id": self.session_id,
            "run_id": self.run_id,
            "team_conversation_id": team_conversation_id,
            "user_input": user_input,
            "timestamp": None,
        }

        headers = [
            ("correlationId", self.run_id.encode("utf-8")),
            ("session_id", self.session_id.encode("utf-8")),
            ("team_conversation_id", team_conversation_id.encode("utf-8")),
        ]

        await self.producer.send(
            KAFKA_HUMAN_INPUT_RESPONSE_TOPIC,
            response,
            headers=headers,
        )

        logger.info(f"Sent human input response: {user_input}")

    async def handle_human_input_request(self, message_data: Dict[str, Any]):
        """Handle human input request from the team with interactive input"""
        prompt = message_data.get("prompt", "")
        team_conversation_id = message_data.get("team_conversation_id", "")
        context = message_data.get("context", [])
        requesting_agent = message_data.get("requesting_agent", "unknown")

        print(f"\n{'='*60}")
        print(f"🤖 HUMAN INPUT REQUESTED by {requesting_agent}")
        print(f"{'='*60}")
        print(f"📝 Prompt: {prompt}")

        if context:
            print(f"\n📚 Recent conversation context:")
            for i, ctx in enumerate(context[-3:], 1):  # Show last 3 messages
                role = ctx.get("role", "unknown")
                content = ctx.get("content", "")
                if len(content) > 100:
                    content = content[:100] + "..."
                print(f"  {i}. [{role}]: {content}")

        print(f"\n{'='*60}")

        # Store the request for potential response
        self.pending_human_inputs[team_conversation_id] = message_data

        # Interactive input - ask user for real input
        try:
            print("\n💭 Please provide your input (or press Enter for auto-response):")
            user_input = input("Your response: ").strip()

            # If no input provided, use smart auto-response
            if not user_input:
                if "clarification" in prompt.lower():
                    user_input = (
                        "Please focus on the technical aspects and provide "
                        "specific examples."
                    )
                elif "preference" in prompt.lower():
                    user_input = (
                        "I prefer the first option with detailed "
                        "implementation steps."
                    )
                elif "information" in prompt.lower():
                    user_input = (
                        "The target audience is software developers with "
                        "intermediate experience."
                    )
                elif "decision" in prompt.lower():
                    user_input = "Please proceed with the recommended approach."
                else:
                    user_input = (
                        "Please continue with your analysis and provide a "
                        "comprehensive summary."
                    )
                print(f"🤖 Auto-response: {user_input}")
            else:
                print(f"✅ Your input: {user_input}")

        except (KeyboardInterrupt, EOFError):
            user_input = "Please continue with the default approach."
            print(f"\n🤖 Using default response: {user_input}")

        # Send the response
        await self.send_human_input_response(team_conversation_id, user_input)

    async def process_responses(self, timeout_seconds: int = 60):
        """Process responses from the orchestration team"""
        logger.info(f"Listening for responses (timeout: {timeout_seconds}s)...")

        start_time = asyncio.get_event_loop().time()
        final_response_received = False

        try:
            while not final_response_received:
                current_time = asyncio.get_event_loop().time()
                if current_time - start_time > timeout_seconds:
                    logger.warning("Timeout reached while waiting for responses")
                    break

                # Poll for messages with timeout
                msg_pack = await self.consumer.getmany(timeout_ms=1000)

                for topic_partition, messages in msg_pack.items():
                    for message in messages:
                        try:
                            message_data = json.loads(message.value.decode("utf-8"))
                            topic = message.topic

                            logger.info(f"Received message on topic {topic}")
                            logger.info(f"Message data: {message_data}")

                            if topic == KAFKA_AGENT_RESPONSE_TOPIC:
                                await self.handle_agent_response(message_data)
                                if message_data.get("final", False):
                                    final_response_received = True
                            elif topic == KAFKA_HUMAN_INPUT_REQUEST_TOPIC:
                                await self.handle_human_input_request(message_data)

                        except Exception as e:
                            logger.error(f"Error processing message: {e}")

        except Exception as e:
            logger.error(f"Error in response processing: {e}")

    async def handle_agent_response(self, message_data: Dict[str, Any]):
        """Handle agent response with improved streaming display"""
        run_id = message_data.get("run_id", "")
        session_id = message_data.get("session_id", "")
        success = message_data.get("success", False)
        final = message_data.get("final", False)
        message = message_data.get("message", "")
        agent_response = message_data.get("agent_response", {})
        human_input_requested = message_data.get("human_input_requested", False)
        human_input = message_data.get("human_input", "")

        # Check if this is our run or if we don't have a session ID yet
        if run_id == self.run_id or (self.session_id is None and session_id):
            # Capture session ID if we don't have one yet
            if self.session_id is None and session_id:
                self.session_id = session_id
                print(f"✅ Session ID captured: {session_id}")

            # Handle human input requests
            if human_input_requested:
                print("\n🤖 HUMAN INPUT REQUESTED 🤖")
                print("=" * 60)
                if agent_response:
                    content = agent_response.get("content", "")
                    agent = agent_response.get("source", "team")
                    if content:
                        print(f"Agent [{agent}] requests: {content}")
                    else:
                        print(f"Agent [{agent}] is requesting human input")
                print("=" * 60)
                return

            # Handle human input acknowledgment
            if human_input:
                print(f"\n✅ Human input received: {human_input}")
                return

            # Display streaming responses
            if agent_response:
                content = agent_response.get("content", "")
                agent = agent_response.get("source", "team")
                message_type = agent_response.get("message_type", "text")

                if content.strip():
                    # Format the output nicely
                    if message_type == "user_input_request":
                        print(f"\n🤖 [{agent}] REQUESTS INPUT: {content}")
                    else:
                        if message_type == "text":
                            print(f"\n🤖 [{agent}]: {content}")

                # Show token usage if available
                models_usage = agent_response.get("models_usage", {})
                if models_usage and (
                    models_usage.get("prompt_tokens", 0) > 0
                    or models_usage.get("completion_tokens", 0) > 0
                ):
                    prompt_tokens = models_usage.get("prompt_tokens", 0)
                    completion_tokens = models_usage.get("completion_tokens", 0)
                    token_msg = (
                        f"   📊 Tokens: {prompt_tokens} prompt + "
                        f"{completion_tokens} completion"
                    )
                    print(token_msg)

            # Show system messages
            elif message:
                if "Human input" in message:
                    print(f"\n💬 {message}")
                elif "Starting" in message or "completed" in message:
                    print(f"\n🔄 {message}")
                else:
                    print(f"\n📝 {message}")

            # Show final status
            if final:
                if success:
                    print("\n✅ Conversation completed successfully!")
                else:
                    print("\n❌ Conversation ended with errors")
                print("=" * 60)

            # Show progress indicator for non-final responses
            elif success and not final:
                print("   ⏳ Processing...")

    async def interactive_chat(self):
        """Interactive chat with the orchestration team"""
        try:
            await self.setup_kafka()

            print("\n🚀 Global Agent Orchestration Team - Interactive Chat")
            print("💡 Chat with the global agent team using specialist coordination")
            print("📝 Type 'quit', 'exit', or 'bye' to end the chat session")
            print("🎯 Multimodal commands:")
            print("   /image - Add sample image attachment")
            print("   /doc - Add sample document attachment")
            print("   /both - Add both image and document attachments")
            print("🤖 Connected to Orchestration Team - Ready to assist!")
            if self.session_id:
                print(f"📋 Session ID: {self.session_id}")
            print("=" * 70)

            while True:
                # Get user input
                user_input = input("\nYou: ").strip()
                if user_input.lower() in ["quit", "exit", "bye"]:
                    print("👋 Goodbye! Chat session ended.")
                    break

                if not user_input:
                    continue

                # Parse multimodal input
                message, attachments = parse_multimodal_input(user_input)

                # Generate new run ID for each message
                self.run_id = str(uuid.uuid4())

                # Send message to orchestration team
                await self.send_orchestration_team_message(
                    message,
                    self.session_id,
                    attachments=attachments,
                )

                # Process responses with timeout
                print("🔄 Processing with orchestration team...")
                await self.process_responses(timeout_seconds=120)

        except KeyboardInterrupt:
            print("\n\n👋 Chat interrupted by user. Goodbye!")
        except Exception as e:
            logger.error(f"Interactive chat failed: {e}")
        finally:
            await self.cleanup_kafka()

    async def run_test(self):
        """Run a single test message (legacy method)"""
        try:
            await self.setup_kafka()

            # Test message that should trigger team discussion and human input
            test_message = """
            I need help designing a microservices architecture for an e-commerce platform.
            The system should handle user authentication, product catalog, shopping cart,
            payment processing, and order management. Please analyze the requirements
            and provide recommendations for technology stack, database design, and
            deployment strategy.
            """

            logger.info("Starting orchestration team test...")
            logger.info(f"Run ID: {self.run_id}")

            # Send the message to orchestration team (will create session automatically)
            await self.send_orchestration_team_message(test_message.strip())

            # Process responses
            await self.process_responses(timeout_seconds=120)

            logger.info("Test completed successfully!")

        except Exception as e:
            logger.error(f"Test failed: {e}")
        finally:
            await self.cleanup_kafka()


async def create_orchestration_session() -> str:
    """Create a new orchestration team session and return the session ID"""
    client = KafkaTestClient()
    await client.initialize()

    try:
        run_id = str(uuid.uuid4())
        creation_request = {
            "user_id": "7b045240-05f2-41e1-b77a-5e464b0ae8be",
            "communication_type": "orchestration_team",
            "run_id": run_id,
            "organization_id": "790db989-a976-4229-9162-37cc7dfd270b",
            "variables": {},
            "mode": "ASK",
            "resource": "ORGANIZATION"
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        logger.info(f"Creating new orchestration session with run_id: {run_id}")
        await client.send_message(
            KAFKA_ORCHESTRATION_TEAM_SESSION_TOPIC, creation_request, headers
        )

        # Wait for response with increased timeout
        responses = await client.listen_for_responses(correlation_id=run_id, timeout=30)

        if not responses:
            raise Exception("No response received for session creation")

        logger.info(f"Received responses: {responses}")

        # Find the response with session_id
        session_id = None
        for response in responses:
            if response.get("session_id"):
                session_id = response.get("session_id")
                break

        if not session_id:
            raise Exception("No session ID found in responses")

        logger.info(
            f"Orchestration session created successfully with session ID: {session_id}"
        )
        return session_id

    except Exception as e:
        logger.error(f"Failed to create orchestration session: {e}")
        raise
    finally:
        await client.cleanup()


async def main():
    """Main function with options for interactive chat or single test"""
    print("🌟 Global Agent Orchestration Team Test")
    print("=" * 50)
    print("Choose your interaction mode:")
    print("1. 💬 Interactive Chat (recommended)")
    print("2. 🧪 Single Test Message")
    print("3. ❓ Help & Commands")

    # Create orchestration session and get session ID
    try:
        session_id = await create_orchestration_session()
        print(f"✅ Session ID: {session_id}")
    except Exception as e:
        print(f"❌ Failed to create orchestration session: {e}")
        return

    while True:
        choice = input(
            "\nEnter your choice (1, 2, 3, or press Enter for default): "
        ).strip()

        if choice == "1" or choice == "":
            # Interactive chat mode (default)
            tester = OrchestrationTeamTester(session_id=session_id)
            await tester.interactive_chat()
            break
        elif choice == "2":
            # Single test mode
            tester = OrchestrationTeamTester(session_id=session_id)
            await tester.run_test()
            break
        elif choice == "3":
            # Help
            print("\n📖 Help & Commands:")
            print("=" * 40)
            print("Interactive Chat Commands:")
            print("  /image - Add sample image attachment")
            print("  /doc - Add sample document attachment")
            print("  /both - Add both image and document attachments")
            print("  quit/exit/bye - End chat session")
            print("\nExample usage:")
            print("  'Analyze this data /image'")
            print("  'Summarize the document /doc'")
            print("  'Compare image and document /both'")
            print("\nFeatures:")
            print("  ✅ Human-in-the-loop interaction")
            print("  ✅ Multimodal support (images, documents)")
            print("  ✅ Global agent coordination")
            print("  ✅ Real-time streaming responses")
            print("  ✅ Session management")
            continue
        else:
            print("❌ Invalid choice. Please enter 1, 2, 3, or press Enter.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 Program interrupted. Goodbye!")
    except Exception as e:
        logger.error(f"Program error: {e}")
        sys.exit(1)
